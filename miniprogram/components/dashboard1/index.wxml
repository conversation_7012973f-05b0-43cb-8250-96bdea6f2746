<!-- 现代化仪表盘组件 -->
<view class="dashboard1-container">

  <!-- Dashboard1 导航栏 -->
  <view class="dashboard1-navbar">
    <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
    <view class="navbar-content">
      <!-- 左侧按钮组 -->
      <view class="navbar-left">
        <view class="navbar-button" bindtap="onShowDashboardSwitcher">
          <text class="button-icon">🔄</text>
        </view>
        <view class="navbar-button" bindtap="onShowSettings">
          <text class="button-icon">⚙️</text>
        </view>
      </view>

      <!-- 中间标题 -->
      <view class="navbar-center">
        <text class="navbar-title">时间跟踪器</text>
      </view>

      <!-- 右侧留空，避免被微信菜单覆盖 -->
      <view class="navbar-right"></view>
    </view>
  </view>

  <!-- 无工作履历引导 -->
  <view wx:if="{{!hasWorkHistory}}" class="no-work-guide">
    <view class="guide-content">
      <view class="guide-icon">💼</view>
      <view class="guide-title">欢迎使用现代仪表盘</view>
      <view class="guide-text">
        <text>请先添加您的工作履历以使用完整功能</text>
      </view>
      <button class="guide-btn" bindtap="goToWorkHistory">
        <text class="btn-icon">✨</text>
        <text>添加工作履历</text>
      </button>
    </view>
  </view>

  <!-- 有工作履历时显示的内容 -->
  <view wx:else class="main-content" style="margin-top: {{navbarHeight}}px; height: calc(100vh - {{navbarHeight}}px);">
    <!-- 顶部区域 -->
    <view class="top-info">
      <!-- 当前时间 -->
      <view class="current-time">
        <text class="time-text">{{currentTime}}</text>
        <text class="date-text">{{currentDateDisplay}}</text>
      </view>

      <!-- 工作信息 -->
      <view class="work-info" wx:if="{{showCurrentWork}}">
        <view class="work-position" bindtap="togglePositionMask">
          <text class="position-text">{{positionMasked ? '***岗位' : currentPosition}}</text>
        </view>
        <view class="work-company" bindtap="toggleCompanyMask">
          <text class="company-text">{{companyMasked ? '***公司' : currentCompany}}</text>
        </view>
      </view>
    </view>

    <!-- 中心区域 -->
    <view class="center-switch-area">
      <!-- 收入显示区域 -->
      <view class="real-time-income" wx:if="{{showCurrentWork}}">
        <view class="income-display">
          <text class="income-label">{{workDateDisplayText}}实时收入</text>
          <view class="current-income" bindtap="togglePrivacyMask" data-field="currentIncome">
            <text>{{currencySymbol}}{{privacyMask.currentIncome ? currentIncomeMasked : currentIncome}}</text>
          </view>
          <view class="hourly-rate" bindtap="togglePrivacyMask" data-field="currentHourlyRate">
            <text>时薪: {{currencySymbol}}{{privacyMask.currentHourlyRate ? currentHourlyRateMasked : currentHourlyRate}}/h</text>
          </view>
        </view>

        <!-- 收入分解 -->
        <view class="income-breakdown">
          <view class="breakdown-item">
            <text class="breakdown-label">工作</text>
            <view class="breakdown-value" bindtap="togglePrivacyMask" data-field="workIncome">
              <text>{{currencySymbol}}{{privacyMask.workIncome ? workIncomeMasked : workIncome}}</text>
            </view>
          </view>
          <view class="breakdown-separator">+</view>
          <view class="breakdown-item">
            <text class="breakdown-label">摸鱼</text>
            <view class="breakdown-value" bindtap="togglePrivacyMask" data-field="fishingIncome">
              <text>{{currencySymbol}}{{privacyMask.fishingIncome ? fishingIncomeMasked : fishingIncome}}</text>
            </view>
          </view>

          <!-- 额外收入（如果不为0则显示） -->
          <view wx:if="{{extraIncomeValue > 0}}" class="breakdown-separator">+</view>
          <view wx:if="{{extraIncomeValue > 0}}" class="breakdown-item extra-income">
            <text class="breakdown-label">额外</text>
            <view class="breakdown-value positive">
              <text>{{currencySymbol}}{{extraIncome}}</text>
            </view>
          </view>

          <!-- 扣款（如果不为0则显示） -->
          <view wx:if="{{deductionsValue > 0}}" class="breakdown-separator">-</view>
          <view wx:if="{{deductionsValue > 0}}" class="breakdown-item deductions">
            <text class="breakdown-label">扣款</text>
            <view class="breakdown-value negative">
              <text>{{currencySymbol}}{{deductions}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 工作时间内显示切换开关 -->
      <view wx:if="{{isInWorkTime}}" class="switch-container">
        <!-- 工作摸鱼切换开关 -->
        <view class="modern-switch-container">
          <view class="modern-switch {{!isFishing ? (currentSegmentType === 'overtime' ? 'overtime' : 'working') : 'fishing'}} {{switchLoading ? 'loading' : ''}}"
                bindtap="onWorkFishingToggle">
            <!-- 背景轨道 -->
            <view class="switch-track-bg"></view>

            <!-- 左侧选项 -->
            <view class="switch-option left {{!isFishing ? 'active' : ''}}">
              <text class="option-icon">{{currentSegmentType === 'overtime' ? '⚡' : '⚡'}}</text>
              <text class="option-text">{{currentSegmentType === 'overtime' ? '加班' : '工作'}}</text>
            </view>

            <!-- 右侧选项 -->
            <view class="switch-option right {{isFishing ? 'active' : ''}}">
              <text class="option-icon">🐟</text>
              <text class="option-text">摸鱼</text>
            </view>

            <!-- 滑动指示器 -->
            <view class="switch-indicator {{!isFishing ? (currentSegmentType === 'overtime' ? 'overtime' : 'working') : 'fishing'}}">
              <view class="loading-spinner" wx:if="{{switchLoading}}"></view>
            </view>
          </view>
        </view>

        <!-- 当前时间段时长显示 -->
        <view class="segment-duration" wx:if="{{isInWorkTime}}">
          <view class="duration-text {{isFishing ? 'fishing' : (currentSegmentType === 'overtime' ? 'overtime' : 'working')}}">
            <text>{{currentSegmentDuration}}</text>
          </view>

          <!-- 状态描述 -->
          <view class="switch-description">
            <text class="description-text" wx:if="{{!isFishing}}">{{currentSegmentType === 'overtime' ? '努力加班中' : '专注工作中'}}</text>
            <view class="fishing-description" wx:else>
              <text class="description-text">愉快</text>
              <text class="fishing-text" bindtap="onEditFishingRemark">{{currentFishingRemark || '摸鱼'}}</text>
              <text class="description-text">中</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 非工作时间显示 -->
      <view wx:else class="non-work-time">
        <!-- 状态显示 -->
        <view class="status-display">
          <text class="status-icon">{{currentStatus.icon}}</text>
          <text class="status-text">{{currentStatus.text}}</text>
        </view>

        <!-- 休息时间段时长显示 -->
        <view class="segment-duration" wx:if="{{currentSegmentType === 'rest' && todaySegments.length > 0}}">
          <text class="duration-text {{currentSegmentType}}">
            {{currentSegmentDuration}}
          </text>
          <text class="duration-label">休息时长</text>
        </view>
      </view>
    </view>

    <!-- 底部区域 -->
    <view class="bottom-stats">

      <!-- 统一的底部信息显示区域 -->
      <view class="unified-info-section">
        <text class="info-text {{infoTextAnimation}}" bindtap="onInfoTextTap">{{currentInfoText}}</text>
      </view>

      <!-- 时间图表 -->
      <view class="time-chart-section" wx:if="{{todaySegments.length > 0}}">
        <time-chart
          segments="{{chartSegments}}"
          fishes="{{todayFishes}}"
          current-fishing-state="{{currentFishingState}}"
          selected-date="{{currentDateText}}"
          height="{{135}}"
          show-current-time="{{true}}"
          is-using-yesterday-data="{{isUsingYesterdayData}}"
          work-date-display-text="{{workDateDisplayText}}">
        </time-chart>
      </view>
    </view>
  </view>

  <!-- 摸鱼备注编辑器 -->
  <fishing-remark-editor
    visible="{{showRemarkEditor}}"
    remark="{{currentFishingRemark}}"
    bindconfirm="onRemarkConfirm"
    bindcancel="onRemarkCancel"
    bindclose="onRemarkCancel">
  </fishing-remark-editor>

  <!-- Dashboard1 设置模态框 -->
  <view wx:if="{{showSettings}}" class="settings-overlay {{settingsVisible ? 'show' : ''}}" bindtap="onHideSettings">
    <view class="settings-content" catchtap="onModalContentTap">
      <view class="settings-header">
        <text class="settings-title">Dashboard1 设置</text>
        <view class="settings-close" bindtap="onHideSettings">✕</view>
      </view>

      <view class="settings-body">
        <view class="setting-item">
          <text class="setting-label">收入小数位数</text>
          <picker
                  mode="selector"
                  range="{{decimalOptions}}"
                  range-key="label"
                  value="{{selectedDecimalIndex}}"
                  bindchange="onDecimalChange">
            <view class="picker-value">{{decimalOptions[selectedDecimalIndex].label}}</view>
          </picker>
        </view>

        <view class="setting-item">
          <text class="setting-label">显示当前工作</text>
          <switch
            checked="{{dashboardConfig.showCurrentWork}}"
            bindchange="onConfigChange"
            data-key="showCurrentWork" />
        </view>

        <view class="setting-item">
          <text class="setting-label">显示实时收入</text>
          <switch
            checked="{{dashboardConfig.showRealTimeIncome}}"
            bindchange="onConfigChange"
            data-key="showRealTimeIncome" />
        </view>

        <view class="setting-item">
          <text class="setting-label">显示快捷操作</text>
          <switch
            checked="{{dashboardConfig.showQuickActions}}"
            bindchange="onConfigChange"
            data-key="showQuickActions" />
        </view>

        <view class="setting-item">
          <text class="setting-label">图表高度 ({{dashboardConfig.chartHeight}}rpx)</text>
          <slider
            min="80"
            max="200"
            value="{{dashboardConfig.chartHeight}}"
            bindchange="onConfigChange"
            data-key="chartHeight"
            show-value />
        </view>
      </view>

      <view class="settings-actions">
        <button class="action-btn cancel" bindtap="onHideSettings">取消</button>
        <button class="action-btn confirm" bindtap="onSaveSettings">保存</button>
      </view>
    </view>
  </view>
</view>
